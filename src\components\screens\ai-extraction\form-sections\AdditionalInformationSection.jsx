import React, { useEffect, useState } from 'react';
import { Select, MenuItem, FormControl } from '@mui/material';
import <PERSON>Field from './components/AiField';
import { getFieldAlertObject } from '../../../../components/utils/aiUtils';
import { CREATION_BUSINESS_ID } from '../../../utils/constants';
import TransportDetailsSection from './TransportDetailsSection';
import OtherInformationSection from './OtherInformationSection';
import ShipToDetailsSection from './ShipToDetailsSection';
import GstSummarySection from './GstSummarySection';

const SECTION = 'additional_information';

export default function AdditionalInformationSection({ formData, isReadOnly, formAction, setFormData }) {
  const data = formData[SECTION] || {};
  const [selectedCutType, setSelectedCutType] = useState(() => {
    if (data?.cut_type === true) return 'cut';
    if (data?.cut_type === false) return 'roll';
    return '';
  });

  useEffect(() => {
    if (selectedCutType === null || selectedCutType === '') {
      formAction('FIELD_CHANGE', SECTION, 'cut_type', '');
    } else {
      const isCutType = selectedCutType === 'cut';
      formAction('FIELD_CHANGE', SECTION, 'cut_type', isCutType);
    }
  }, [selectedCutType]);

  return (
    <div className="flex flex-col gap-3">
      {formData?.business_id === CREATION_BUSINESS_ID && (
        <AiField
          label="Type"
          isExactMatch={data?.exact_match?.type}
          alertObject={getFieldAlertObject(data, 'type')}
          className="only-1-column"
          required
          renderCustomField={() => (
            <FormControl fullWidth>
              <Select
                value={selectedCutType}
                onChange={(e) => setSelectedCutType(e.target.value)}
                disabled={isReadOnly}
                displayEmpty
                className="input-field"
                size="small"
              >
                <MenuItem value="">
                  <em>Select Type</em>
                </MenuItem>
                <MenuItem value="cut">Cut</MenuItem>
                <MenuItem value="roll">Roll</MenuItem>
              </Select>
            </FormControl>
          )}
        />
      )}

      <Section title="GST Summary">
        <GstSummarySection
          formData={formData}
          isReadOnly={isReadOnly}
          formAction={formAction}
          setFormData={setFormData}
        />
      </Section>

      <Section title="Ship To Details">
        <ShipToDetailsSection formData={formData} isReadOnly={isReadOnly} formAction={formAction} />
      </Section>

      <Section title="Transport Details">
        <TransportDetailsSection formData={formData} isReadOnly={isReadOnly} formAction={formAction} />
      </Section>

      <Section title="Other Information">
        <OtherInformationSection formData={formData} isReadOnly={isReadOnly} formAction={formAction} />
      </Section>

      <AiField
        label="Remarks"
        isExactMatch={data?.exact_match?.remarks}
        alertObject={getFieldAlertObject(data, 'remarks')}
        Element="textarea"
        name="remarks"
        id="remarks"
        value={data?.remarks ?? ''}
        className="only-1-column"
        onChange={(e) => formAction('FIELD_CHANGE', SECTION, 'remarks', e.target.value)}
        disabled={isReadOnly}
        rows={2}
      />
    </div>
  );
}

function Section({ title, children }) {
  return (
    <div className="flex flex-col">
      <h2 className="text-xs font-semibold text-gray-600 uppercase tracking-wide mb-3 pb-1 border-b border-gray-200">
        {title}
      </h2>
      <div className="mx-2">{children}</div>
    </div>
  );
}
